#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel文件整理脚本
将三个Excel文件根据订单号进行匹配，生成新的整理后的Excel文件
"""

import pandas as pd
import re
import os
from typing import Dict, List, Optional

def extract_order_number(text: str) -> Optional[str]:
    """
    从文本中提取订单号（Z开头23位数）
    
    Args:
        text: 输入文本
        
    Returns:
        订单号或None
    """
    if pd.isna(text) or not isinstance(text, str):
        return None
    
    # 匹配Z开头的23位订单号
    pattern = r'Z\d{22}'
    match = re.search(pattern, text)
    return match.group(0) if match else None

def load_excel_files() -> Dict[str, pd.DataFrame]:
    """
    加载三个Excel文件
    
    Returns:
        包含三个DataFrame的字典
    """
    files = {}
    
    try:
        # 加载A文件
        files['A'] = pd.read_excel('A.xlsx')
        print(f"A文件加载成功，共{len(files['A'])}行数据")
        
        # 加载B文件
        files['B'] = pd.read_excel('B.xlsx')
        print(f"B文件加载成功，共{len(files['B'])}行数据")
        
        # 加载C文件
        files['C'] = pd.read_excel('C.xlsx')
        print(f"C文件加载成功，共{len(files['C'])}行数据")
        
    except FileNotFoundError as e:
        print(f"文件未找到: {e}")
        raise
    except Exception as e:
        print(f"加载文件时出错: {e}")
        raise
    
    return files

def process_file_a(df_a: pd.DataFrame) -> pd.DataFrame:
    """
    处理A文件，提取订单号
    
    Args:
        df_a: A文件的DataFrame
        
    Returns:
        处理后的DataFrame
    """
    # 假设A文件的列名，您可能需要根据实际情况调整
    # 常见的列名可能是：评论内容、评论用户、帖子内容等
    
    # 创建订单号列
    if '评论内容' in df_a.columns:
        df_a['订单号'] = df_a['评论内容'].apply(extract_order_number)
    else:
        # 如果列名不同，尝试从第一列提取
        first_col = df_a.columns[0]
        df_a['订单号'] = df_a[first_col].apply(extract_order_number)
        print(f"警告：未找到'评论内容'列，使用'{first_col}'列提取订单号")
    
    # 过滤掉没有订单号的行
    df_a_filtered = df_a[df_a['订单号'].notna()].copy()
    print(f"A文件中找到{len(df_a_filtered)}条包含订单号的记录")
    
    return df_a_filtered

def process_file_b(df_b: pd.DataFrame) -> pd.DataFrame:
    """
    处理B文件，提取订单号
    
    Args:
        df_b: B文件的DataFrame
        
    Returns:
        处理后的DataFrame
    """
    # 假设B文件中有订单号列或需要从某列提取
    if '订单号' not in df_b.columns:
        # 尝试从各列中提取订单号
        for col in df_b.columns:
            df_b['订单号'] = df_b[col].apply(extract_order_number)
            if df_b['订单号'].notna().any():
                break
    
    # 过滤掉没有订单号的行
    df_b_filtered = df_b[df_b['订单号'].notna()].copy()
    print(f"B文件中找到{len(df_b_filtered)}条包含订单号的记录")
    
    return df_b_filtered

def process_file_c(df_c: pd.DataFrame) -> pd.DataFrame:
    """
    处理C文件，提取订单号
    
    Args:
        df_c: C文件的DataFrame
        
    Returns:
        处理后的DataFrame
    """
    # 假设C文件中有订单号列或需要从某列提取
    if '订单号' not in df_c.columns:
        # 尝试从各列中提取订单号
        for col in df_c.columns:
            df_c['订单号'] = df_c[col].apply(extract_order_number)
            if df_c['订单号'].notna().any():
                break
    
    # 过滤掉没有订单号的行
    df_c_filtered = df_c[df_c['订单号'].notna()].copy()
    print(f"C文件中找到{len(df_c_filtered)}条包含订单号的记录")
    
    return df_c_filtered

def merge_data(df_a: pd.DataFrame, df_b: pd.DataFrame, df_c: pd.DataFrame) -> pd.DataFrame:
    """
    根据订单号合并三个文件的数据
    
    Args:
        df_a: 处理后的A文件DataFrame
        df_b: 处理后的B文件DataFrame
        df_c: 处理后的C文件DataFrame
        
    Returns:
        合并后的DataFrame
    """
    # 以A文件为基础进行左连接
    merged_df = df_a.copy()
    
    # 与B文件合并
    merged_df = pd.merge(merged_df, df_b, on='订单号', how='left', suffixes=('', '_B'))
    
    # 与C文件合并
    merged_df = pd.merge(merged_df, df_c, on='订单号', how='left', suffixes=('', '_C'))
    
    print(f"合并后共有{len(merged_df)}条记录")
    
    return merged_df

def create_final_excel(merged_df: pd.DataFrame) -> pd.DataFrame:
    """
    创建最终的Excel格式
    
    Args:
        merged_df: 合并后的DataFrame
        
    Returns:
        最终格式的DataFrame
    """
    # 创建新的DataFrame，按照要求的列顺序
    final_columns = [
        '评论内容', '评论用户', '帖子内容', '图片', 
        '订单金额', '用户累积消费次数', '是否为年卡用户', 
        '订单状态', '收货地址', '手机号', '分销员'
    ]
    
    final_df = pd.DataFrame()
    
    # 从A文件获取的列
    a_columns = ['评论内容', '评论用户', '帖子内容']
    for col in a_columns:
        if col in merged_df.columns:
            final_df[col] = merged_df[col]
        else:
            # 如果列名不匹配，尝试找到相似的列
            available_cols = [c for c in merged_df.columns if not c.endswith('_B') and not c.endswith('_C')]
            print(f"警告：未找到列'{col}'，可用列：{available_cols}")
            final_df[col] = None
    
    # 从B文件获取的列
    b_columns = ['图片']
    for col in b_columns:
        b_col_name = col + '_B' if col + '_B' in merged_df.columns else col
        if b_col_name in merged_df.columns:
            final_df[col] = merged_df[b_col_name]
        else:
            print(f"警告：未找到B文件中的列'{col}'")
            final_df[col] = None
    
    # 从C文件获取的列
    c_columns = ['订单金额', '用户累积消费次数', '是否为年卡用户', '订单状态', '收货地址', '手机号', '分销员']
    for col in c_columns:
        c_col_name = col + '_C' if col + '_C' in merged_df.columns else col
        if c_col_name in merged_df.columns:
            final_df[col] = merged_df[c_col_name]
        else:
            print(f"警告：未找到C文件中的列'{col}'")
            final_df[col] = None
    
    return final_df

def main():
    """
    主函数
    """
    try:
        print("开始处理Excel文件...")
        
        # 1. 加载文件
        files = load_excel_files()
        
        # 2. 处理各个文件
        df_a_processed = process_file_a(files['A'])
        df_b_processed = process_file_b(files['B'])
        df_c_processed = process_file_c(files['C'])
        
        # 3. 合并数据
        merged_df = merge_data(df_a_processed, df_b_processed, df_c_processed)
        
        # 4. 创建最终格式
        final_df = create_final_excel(merged_df)
        
        # 5. 保存结果
        output_file = '整理后的数据.xlsx'
        final_df.to_excel(output_file, index=False)
        print(f"处理完成！结果已保存到：{output_file}")
        print(f"最终文件包含{len(final_df)}行数据")
        
        # 显示前几行数据预览
        print("\n数据预览：")
        print(final_df.head())
        
    except Exception as e:
        print(f"处理过程中出现错误：{e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
